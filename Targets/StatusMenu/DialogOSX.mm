//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

#include "Dialog.hpp"

#if defined(__WXOSX__) && wxOSX_USE_COCOA
#import <Cocoa/Cocoa.h>
#include <wx/osx/cocoa/private.h>

void MyDialog::ApplyMessageBoxStyle()
{
    NSWindow* window = (NSWindow*)GetWXWindow();
    if (window) {
        // Set the window appearance to match macOS alert dialogs
        window.appearance = [NSAppearance appearanceNamed:NSAppearanceNameVibrantLight];
        
        // Make the window background translucent with blur effect
        window.backgroundColor = [NSColor clearColor];
        
        // Create a visual effect view with the HUD style (dark, translucent)
        NSVisualEffectView* effectView = [[NSVisualEffectView alloc] initWithFrame:[[window contentView] bounds]];
        effectView.material = NSVisualEffectMaterialSheet; // This matches the alert dialog style
        effectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
        effectView.state = NSVisualEffectStateActive;
        effectView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
        
        // Insert the visual effect view at the bottom of the view hierarchy
        [[window contentView] addSubview:effectView positioned:NSWindowBelow relativeTo:nil];
    }
}
#endif