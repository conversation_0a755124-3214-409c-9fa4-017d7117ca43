//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

#pragma once

// Project includes
// External includes
#include <wx/wx.h>
#include <wx/taskbar.h>
// System includes

class MyTaskBarIcon final : public wxTaskBarIcon
{
public:
    #if defined(__WXOSX__) && wxOSX_USE_COCOA
    explicit MyTaskBarIcon(wxTaskBarIconType iconType = wxTBI_DEFAULT_TYPE);
    #else
    MyTaskBarIcon();
    #endif

    wxMenu* CreatePopupMenu() override;

    void OnLeftButtonDClick(wxTaskBarIconEvent&);
    void OnMenuRestore(wxCommandEvent&);
    void OnMenuExit(wxCommandEvent&);
    void OnMenuSetNewIcon(wxCommandEvent&);
    void OnMenuCheckmark(wxCommandEvent&);
    void OnMenuUICheckmark(wxUpdateUIEvent&);
    void OnMenuSub(wxCommandEvent&);
};
