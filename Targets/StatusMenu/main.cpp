//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
#include "App.hpp"
#include "Dialog.hpp"
// External includes
#include <wx/wx.h>
#include <wx/taskbar.h>
// System includes

bool MyApp::OnInit()
{
    if (!wxApp::OnInit())
        return false;

    #if not defined(__WXOSX__) && wxOSX_USE_COCOA
    if (!wxTaskBarIcon::IsAvailable())
    {
        wxMessageBox("There appears to be no system tray support in your current environment.",
                     "Warning", wxOK | wxICON_EXCLAMATION);
    }
    #endif

    // Create the main window
    m_dialog = new MyDialog("wxTaskBarIcon Test Dialog");
    m_dialog->Show(true);

    return true;
}

MyDialog* MyApp::getDialog() const
{
    return m_dialog;
}

wxIMPLEMENT_APP(MyApp);
