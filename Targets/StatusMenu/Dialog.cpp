//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
#include "Dialog.hpp"
#include "TaskBarIcon.hpp"
// External includes
#include <wx/wx.h>
#include <wx/artprov.h>
// System includes


MyDialog::MyDialog(const wxString& title)
    : wxMessageDialog(nullptr, 
                     "Press 'Hide me' to hide this window, Exit to quit.",
                     title,
                     wxOK | wxCANCEL | wxICON_INFORMATION)
{
    // Create and set up the taskbar icon
    m_taskBarIcon = new MyTaskBarIcon();

    // we should be able to show up to 128 characters on Windows.
    if (!m_taskBarIcon->SetIcon(wxArtProvider::GetBitmapBundle(wxART_WX_LOGO, wxART_OTHER, wxSize(32, 32)),
                                "wxTaskBarIcon Sample\n"
                                "With a very, very, very, very\n"
                                "long tooltip whose length is\n"
                                "greater than 64 characters.") )
    {
        wxLogError("Could not set icon.");
    }

    #if defined(__WXOSX__) && wxOSX_USE_COCOA
    m_dockIcon = new MyTaskBarIcon(wxTBI_DOCK);
    if (!m_dockIcon->SetIcon(wxArtProvider::GetBitmapBundle(wxART_WX_LOGO, wxART_OTHER, wxSize(32, 32))))
    {
        wxLogError("Could not set icon.");
    }
    #endif

    // Add custom buttons
    SetOKLabel("Hide");

    // Bind events
    Bind(wxEVT_BUTTON, &MyDialog::OnOK, this, wxID_OK);
    Bind(wxEVT_BUTTON, &MyDialog::OnExit, this, wxID_CANCEL);
    Bind(wxEVT_CLOSE_WINDOW, &MyDialog::OnClose, this);
    
    // Add an About button
    SetExtendedMessage("wxWidgets Taskbar Sample");
    
    // Center the dialog
    Centre();
}

MyDialog::~MyDialog()
{
    delete m_taskBarIcon;
    #if defined(__WXOSX__) && wxOSX_USE_COCOA
    delete m_dockIcon;
    #endif
}

void MyDialog::OnAbout(wxCommandEvent& /*event*/)
{
    static const char * const title = "About wxWidgets Taskbar Sample";
    static const char * const message
        = "wxWidgets sample showing wxTaskBarIcon class\n"
          "\n"
          "(C) 2025 Arkin Terli";

    #if defined(__WXMSW__) && wxUSE_TASKBARICON_BALLOONS
    m_taskBarIcon->ShowBalloon(title, message, 15000, wxICON_INFORMATION,
                               wxBitmapBundle::FromSVGFile("info.svg", wxSize(64, 64)));
    #else // !__WXMSW__
    wxMessageBox(message, title, wxICON_INFORMATION | wxOK, this);
    #endif // __WXMSW__/!__WXMSW__
}

void MyDialog::OnOK(wxCommandEvent& /*event*/)
{
    Show(false);
}

void MyDialog::OnExit(wxCommandEvent& /*event*/)
{
    Close(true);
}

void MyDialog::OnClose(wxCloseEvent& /*event*/)
{
    Destroy();
}
