//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

#pragma once

// Project includes
#include "Dialog.hpp"
// External includes
#include <wx/wx.h>
// System includes

class MyDialog;

class MyApp final : public wxApp
{
public:
    bool OnInit() override;
    MyDialog* getDialog() const;

private:
    MyDialog* m_dialog{nullptr};
};

wxDECLARE_APP(MyApp);
