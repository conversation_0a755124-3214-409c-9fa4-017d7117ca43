//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

#pragma once

// Project includes
// External includes
#include <wx/wx.h>
// System includes

class MyTaskBarIcon;

class MyDialog final : public wxDialog
{
public:
    // Constructor.
    explicit MyDialog(const wxString& title);
    // Destructor.
    ~MyDialog() override;

protected:
    void OnAbout(wxCommandEvent& event);
    void OnOK(wxCommandEvent& event);
    void OnExit(wxCommandEvent& event);
    void OnClose(wxCloseEvent& event);

    MyTaskBarIcon* m_taskBarIcon{nullptr};

    #if defined(__WXOSX__) && wxOSX_USE_COCOA
    MyTaskBarIcon* m_dockIcon{nullptr};
    void ApplyMessageBoxStyle();  // New method to apply the style
    #endif
};
