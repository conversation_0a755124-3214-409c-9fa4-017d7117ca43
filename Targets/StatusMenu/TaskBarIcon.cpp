//
//  Copyright © 2025-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
#include "App.hpp"
#include "TaskBarIcon.hpp"
// External includes
#include <wx/wx.h>
// System includes


enum
{
    PU_RESTORE = 10001,
    PU_NEW_ICON,
    PU_EXIT,
    PU_CHECKMARK,
    PU_SUB1,
    PU_SUB2,
    PU_SUBMAIN
};


#if defined(__WXOSX__) && wxOSX_USE_COCOA
MyTaskBarIcon::MyTaskBarIcon(wxTaskBarIconType iconType) : wxTaskBarIcon(iconType)
#else
MyTaskBarIcon::MyTaskBarIcon()
#endif
{
    // Bind events instead of using event table
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuRestore, this, PU_RESTORE);
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuExit, this, PU_EXIT);
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuSetNewIcon, this, PU_NEW_ICON);
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuCheckmark, this, PU_CHECKMARK);
    Bind(wxEVT_UPDATE_UI, &MyTaskBarIcon::OnMenuUICheckmark, this, PU_CHECKMARK);
    Bind(wxEVT_TASKBAR_LEFT_DCLICK, &MyTaskBarIcon::OnLeftButtonDClick, this);
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuSub, this, PU_SUB1);
    Bind(wxEVT_MENU, &MyTaskBarIcon::OnMenuSub, this, PU_SUB2);
}

void MyTaskBarIcon::OnMenuRestore(wxCommandEvent& /*event*/)
{
    wxGetApp().getDialog()->Show(true);
}

void MyTaskBarIcon::OnMenuExit(wxCommandEvent& /*event*/)
{
    wxGetApp().getDialog()->Close(true);
}

static bool check = true;

void MyTaskBarIcon::OnMenuCheckmark(wxCommandEvent& /*event*/)
{
    check = !check;
}

void MyTaskBarIcon::OnMenuUICheckmark(wxUpdateUIEvent& event)
{
    event.Check(check);
}

void MyTaskBarIcon::OnMenuSetNewIcon(wxCommandEvent& /*event*/)
{
    // wxIcon icon(smile_xpm);
    //
    // if (!SetIcon(wxBitmapBundle::FromBitmaps(
    //                  wxBitmap(smile_xpm),
    //                  wxBitmap(smile2_xpm)
    //              ),
    //              "wxTaskBarIcon Sample - a different icon"))
    //     wxMessageBox("Could not set new icon.");
}

void MyTaskBarIcon::OnMenuSub(wxCommandEvent& /*event*/)
{
    wxMessageBox("You clicked on a submenu!");
}

wxMenu* MyTaskBarIcon::CreatePopupMenu()
{
    const auto menu = new wxMenu;
    menu->Append(PU_RESTORE, "&Restore main window");
    menu->AppendSeparator();
    menu->Append(PU_NEW_ICON, "&Set New Icon");
    menu->AppendSeparator();
    menu->AppendCheckItem(PU_CHECKMARK, "Test &check mark");
    menu->AppendSeparator();
    const auto submenu = new wxMenu;
    submenu->Append(PU_SUB1, "One submenu");
    submenu->AppendSeparator();
    submenu->Append(PU_SUB2, "Another submenu");
    menu->Append(PU_SUBMAIN, "Submenu", submenu);

    // OSX has built-in quit menu for the dock menu, but not for the status item.

    #ifdef __WXOSX__
    if (OSXIsStatusItem())
    #endif
    {
        menu->AppendSeparator();
        menu->Append(PU_EXIT, "E&xit");
    }

    return menu;
}

void MyTaskBarIcon::OnLeftButtonDClick(wxTaskBarIconEvent& /*event*/)
{
    wxGetApp().getDialog()->Show(true);
}
